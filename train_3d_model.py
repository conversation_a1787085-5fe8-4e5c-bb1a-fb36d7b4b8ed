#!/usr/bin/env python3
"""
训练3D避障学生模型
"""
import os
import argparse
import numpy as np
import matplotlib.pyplot as plt

from models.student_model import StudentModel
from training.trainer import Trainer
from config import EPOCHS, BATCH_SIZE, MODEL_DIR

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Train 3D obstacle avoidance student model")
    parser.add_argument("--data_path", type=str, required=True, help="Path to training data file")
    parser.add_argument("--epochs", type=int, default=EPOCHS, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=BATCH_SIZE, help="Batch size")
    parser.add_argument("--model_dir", type=str, default=MODEL_DIR, help="Directory to save model")
    parser.add_argument("--test_split", type=float, default=0.2, help="Test data split ratio")
    
    return parser.parse_args()

def load_and_validate_data(data_path):
    """
    加载和验证数据
    """
    print(f"加载数据: {data_path}")
    
    # 加载数据
    data = np.load(data_path, allow_pickle=True)
    observations = data["observations"]
    actions = data["actions"]
    
    print(f"观测数据形状: {observations.shape}")
    print(f"动作数据形状: {actions.shape}")
    
    # 验证数据格式
    if len(observations.shape) != 3:
        raise ValueError(f"观测数据应该是3维 (samples, height, width)，实际为: {observations.shape}")
    
    if len(actions.shape) != 2 or actions.shape[1] != 3:
        raise ValueError(f"动作数据应该是2维 (samples, 3)，实际为: {actions.shape}")
    
    print("✅ 数据格式验证通过")
    
    # 数据统计
    print(f"\n数据统计:")
    print(f"样本数量: {len(observations)}")
    print(f"深度图像尺寸: {observations.shape[1]}x{observations.shape[2]}")
    print(f"深度值范围: [{np.min(observations):.2f}, {np.max(observations):.2f}]")
    print(f"速度向量统计:")
    for i, dim in enumerate(['Vx', 'Vy', 'Vz']):
        print(f"  {dim}: 范围=[{np.min(actions[:, i]):.2f}, {np.max(actions[:, i]):.2f}], "
              f"平均={np.mean(actions[:, i]):.2f}, 标准差={np.std(actions[:, i]):.2f}")
    
    return observations, actions

def split_data(observations, actions, test_split=0.2):
    """
    分割训练和测试数据
    """
    num_samples = len(observations)
    num_test = int(num_samples * test_split)
    
    # 随机打乱数据
    indices = np.random.permutation(num_samples)
    
    # 分割数据
    test_indices = indices[:num_test]
    train_indices = indices[num_test:]
    
    train_obs = observations[train_indices]
    train_actions = actions[train_indices]
    test_obs = observations[test_indices]
    test_actions = actions[test_indices]
    
    print(f"\n数据分割:")
    print(f"训练集: {len(train_obs)} 样本")
    print(f"测试集: {len(test_obs)} 样本")
    
    return train_obs, train_actions, test_obs, test_actions

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 创建保存目录
    os.makedirs(args.model_dir, exist_ok=True)
    
    try:
        # 加载和验证数据
        observations, actions = load_and_validate_data(args.data_path)
        
        # 分割数据
        train_obs, train_actions, test_obs, test_actions = split_data(
            observations, actions, args.test_split
        )
        
        # 创建学生模型
        print(f"\n创建3D学生模型...")
        student = StudentModel()
        
        # 创建训练器
        trainer = Trainer(student, args.model_dir)
        
        # 训练模型
        print(f"\n开始训练...")
        print(f"训练参数: epochs={args.epochs}, batch_size={args.batch_size}")
        
        loss_history = trainer.train(
            train_obs, train_actions, 
            epochs=args.epochs, 
            batch_size=args.batch_size
        )
        
        # 绘制损失曲线
        trainer.plot_loss(loss_history)
        
        # 在测试集上评估
        print(f"\n在测试集上评估模型...")
        test_mae = trainer.evaluate(test_obs, test_actions)
        
        # 在训练集上评估（用于对比）
        print(f"\n在训练集上评估模型...")
        train_mae = trainer.evaluate(train_obs, train_actions)
        
        # 总结
        print(f"\n" + "="*50)
        print(f"训练完成！")
        print(f"最终训练损失: {loss_history[-1]:.6f}")
        print(f"训练集MAE: {train_mae:.6f}")
        print(f"测试集MAE: {test_mae:.6f}")
        
        if test_mae < train_mae * 1.5:
            print("✅ 模型泛化性能良好")
        else:
            print("⚠️ 模型可能存在过拟合")
        
        print(f"模型已保存到: {args.model_dir}")
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
