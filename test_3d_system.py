#!/usr/bin/env python3
"""
测试3D避障系统
"""
import numpy as np
import matplotlib.pyplot as plt

from models.teacher_model import TeacherModel
from models.student_model import StudentModel
from environment.vfh3d_utils import VFH3DPlanner

def test_teacher_model():
    """
    测试教师模型
    """
    print("测试教师模型...")
    
    # 创建教师模型
    teacher = TeacherModel()
    
    # 创建模拟深度图像
    depth_img = np.full((72, 128), 50.0)  # 默认50米距离
    
    # 添加一些障碍物
    depth_img[30:42, 50:70] = 8.0  # 前方障碍物
    depth_img[20:30, 80:100] = 12.0  # 右前方障碍物
    depth_img[45:55, 30:50] = 15.0  # 左前方障碍物
    
    # 获取安全速度
    safe_velocity = teacher.get_safe_velocity(depth_img)
    
    print(f"教师模型输出的安全速度向量: {safe_velocity}")
    
    return safe_velocity

def test_student_model():
    """
    测试学生模型
    """
    print("测试学生模型...")
    
    # 创建学生模型
    student = StudentModel()
    
    # 创建模拟深度图像
    depth_img = np.random.rand(72, 128) * 50.0  # 随机深度图像
    
    # 预测速度向量
    predicted_velocity = student.predict(depth_img)
    
    print(f"学生模型输出的速度向量: {predicted_velocity}")
    
    return predicted_velocity

def test_vfh3d_planner():
    """
    测试3DVFH+规划器
    """
    print("测试3DVFH+规划器...")
    
    # 创建规划器
    planner = VFH3DPlanner()
    
    # 创建测试点云
    points = []
    
    # 前方障碍物墙
    for x in np.linspace(8, 12, 20):
        for z in np.linspace(-2, 2, 10):
            points.append([x, 0, z])
    
    # 右侧障碍物
    for y in np.linspace(3, 6, 15):
        for z in np.linspace(-1, 3, 8):
            points.append([5, y, z])
    
    points = np.array(points)
    print(f"创建了 {len(points)} 个障碍物点")
    
    # 当前速度（向前飞行）
    current_velocity = np.array([3.0, 0.0, 0.0])
    
    # 规划速度
    planned_velocity = planner.plan_velocity(points, current_velocity)
    
    print(f"VFH3D规划器输出的速度向量: {planned_velocity}")
    
    return planned_velocity

def test_data_compatibility():
    """
    测试数据兼容性
    """
    print("测试数据兼容性...")
    
    # 创建模拟训练数据
    num_samples = 100
    observations = np.random.rand(num_samples, 72, 128) * 50.0  # 深度图像
    actions = np.random.rand(num_samples, 3) * 6.0 - 3.0  # 3维速度向量
    
    print(f"观测数据形状: {observations.shape}")
    print(f"动作数据形状: {actions.shape}")
    
    # 测试学生模型训练
    student = StudentModel()
    
    try:
        # 训练几个epoch
        loss_history = student.train(observations, actions, epochs=5, batch_size=16)
        print(f"训练损失: {loss_history}")
        print("数据兼容性测试通过！")
        return True
    except Exception as e:
        print(f"数据兼容性测试失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("开始测试3D避障系统...")
    print("=" * 50)
    
    try:
        # 测试VFH3D规划器
        print("\n1. 测试VFH3D规划器")
        vfh_velocity = test_vfh3d_planner()
        
        # 测试教师模型
        print("\n2. 测试教师模型")
        teacher_velocity = test_teacher_model()
        
        # 测试学生模型
        print("\n3. 测试学生模型")
        student_velocity = test_student_model()
        
        # 测试数据兼容性
        print("\n4. 测试数据兼容性")
        compatibility_ok = test_data_compatibility()
        
        # 总结
        print("\n" + "=" * 50)
        print("测试结果总结:")
        print(f"VFH3D规划器输出: {vfh_velocity}")
        print(f"教师模型输出: {teacher_velocity}")
        print(f"学生模型输出: {student_velocity}")
        print(f"数据兼容性: {'通过' if compatibility_ok else '失败'}")
        
        if compatibility_ok:
            print("\n✅ 所有测试通过！3D避障系统准备就绪。")
        else:
            print("\n❌ 部分测试失败，请检查代码。")
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
