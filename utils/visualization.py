"""
可视化工具
"""
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Wedge
from mpl_toolkits.mplot3d import Axes3D
import os
import time
from typing import List, Tuple, Dict, Any

def plot_distance_data(distance_data: np.ndarray, action = None, save_path: str = None) -> None:
    """
    绘制距离数据（雷达或点云转换的距离数组）

    Args:
        distance_data: 距离数据
        action: 转向角或3维速度向量
        save_path: 保存路径
    """
    # 创建极坐标图
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw={'projection': 'polar'})

    # 计算角度（弧度）- 只显示90度视场角
    num_bins = len(distance_data)
    angle_range = 90.0  # 度
    half_range = angle_range / 2

    # 创建角度数组（弧度）
    angles = np.linspace(-np.radians(half_range), np.radians(half_range), num_bins)

    # 绘制距离数据
    ax.plot(angles, distance_data, 'o-', linewidth=2)

    # 填充距离数据
    ax.fill(angles, distance_data, alpha=0.25)

    # 设置方向标签
    ax.set_theta_zero_location('N')  # 0度在上方（北）
    ax.set_theta_direction(-1)  # 顺时针方向

    # 设置半径范围
    max_range = max(10.0, np.max(distance_data) * 1.1)
    ax.set_ylim(0, max_range)

    # 设置角度范围
    ax.set_thetamin(-half_range)
    ax.set_thetamax(half_range)

    # 添加标题
    title = "Distance Data"
    if action is not None:
        if isinstance(action, (list, tuple, np.ndarray)) and len(action) == 3:
            title += f" (Velocity: Vx={action[0]:.2f}, Vy={action[1]:.2f}, Vz={action[2]:.2f})"
        else:
            title += f" (Action: {action:.2f}°)"
    ax.set_title(title)

    # 添加网格
    ax.grid(True)

    # 如果指定了保存路径，保存图像
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def plot_depth_image(depth_img: np.ndarray, action = None, save_path: str = None) -> None:
    """
    绘制深度图像

    Args:
        depth_img: 深度图像
        action: 转向角或3维速度向量
        save_path: 保存路径
    """
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 6))

    # 绘制深度图像
    im = ax.imshow(depth_img, cmap='jet')

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('Depth (m)')

    # 添加标题
    title = "Depth Image"
    if action is not None:
        if isinstance(action, (list, tuple, np.ndarray)) and len(action) == 3:
            title += f" (Velocity: Vx={action[0]:.2f}, Vy={action[1]:.2f}, Vz={action[2]:.2f})"
        else:
            title += f" (Action: {action:.2f}°)"
    ax.set_title(title)

    # 如果指定了保存路径，保存图像
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def plot_point_cloud(points: np.ndarray, save_path: str = None) -> None:
    """
    绘制点云

    Args:
        points: 点云数据 (N, 3)
        save_path: 保存路径
    """
    # 创建3D图形
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # 只保留前方90度视场角内的点
    # 计算每个点的水平角度
    angles = np.arctan2(points[:, 1], points[:, 0])  # 弧度，[-pi, pi]
    angles_deg = np.degrees(angles)  # [-180, 180]

    # 过滤点云，只保留前方90度视场角内的点
    mask = (angles_deg >= -45) & (angles_deg <= 45)
    filtered_points = points[mask]

    if len(filtered_points) == 0:
        filtered_points = points  # 如果过滤后没有点，使用原始点云

    # 绘制点云，使用深度值作为颜色
    scatter = ax.scatter(
        filtered_points[:, 0],
        filtered_points[:, 1],
        filtered_points[:, 2],
        c=filtered_points[:, 0],  # 使用X值（深度）作为颜色
        cmap='jet',
        marker='.',
        s=2
    )

    # 设置坐标轴标签
    ax.set_xlabel('X (forward)')
    ax.set_ylabel('Y (right)')
    ax.set_zlabel('Z (down)')

    # 添加标题
    ax.set_title("Point Cloud")

    # 设置坐标轴范围
    max_range = np.max([np.max(np.abs(filtered_points[:, 0])),
                        np.max(np.abs(filtered_points[:, 1])),
                        np.max(np.abs(filtered_points[:, 2]))])
    max_range = max(max_range, 10.0)  # 确保至少有10米的可视范围

    ax.set_xlim([0, max_range])  # 只显示前方
    ax.set_ylim([-max_range/2, max_range/2])
    ax.set_zlim([-max_range/2, max_range/2])

    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('X (m)')

    # 如果指定了保存路径，保存图像
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def plot_velocity_vector(velocity: np.ndarray, save_path: str = None) -> None:
    """
    绘制3维速度向量

    Args:
        velocity: 3维速度向量 (Vx, Vy, Vz)
        save_path: 保存路径
    """
    # 创建3D图形
    fig = plt.figure(figsize=(8, 8))
    ax = fig.add_subplot(111, projection='3d')

    # 绘制速度向量
    vx, vy, vz = velocity
    ax.quiver(0, 0, 0, vx, vy, vz, color='red', arrow_length_ratio=0.1, linewidth=3)

    # 设置坐标轴
    max_v = max(abs(vx), abs(vy), abs(vz), 1.0)
    ax.set_xlim([-max_v, max_v])
    ax.set_ylim([-max_v, max_v])
    ax.set_zlim([-max_v, max_v])

    ax.set_xlabel('Vx (forward)')
    ax.set_ylabel('Vy (right)')
    ax.set_zlabel('Vz (up)')

    ax.set_title(f'3D Velocity Vector\nVx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}')

    # 如果指定了保存路径，保存图像
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def plot_trajectory(positions: List[Tuple[float, float]], save_path: str = None) -> None:
    """
    绘制轨迹

    Args:
        positions: 位置列表 [(x1, y1), (x2, y2), ...]
        save_path: 保存路径
    """
    # 提取x和y坐标
    x = [pos[0] for pos in positions]
    y = [pos[1] for pos in positions]

    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 10))

    # 绘制轨迹
    ax.plot(x, y, 'b-', linewidth=2)

    # 标记起点和终点
    ax.plot(x[0], y[0], 'go', markersize=10, label='Start')
    ax.plot(x[-1], y[-1], 'ro', markersize=10, label='End')

    # 添加标题和标签
    ax.set_title("Drone Trajectory")
    ax.set_xlabel("X (m)")
    ax.set_ylabel("Y (m)")

    # 添加图例
    ax.legend()

    # 添加网格
    ax.grid(True)

    # 保持纵横比例一致
    ax.set_aspect('equal')

    # 如果指定了保存路径，保存图像
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def visualize_drone_state(position: Tuple[float, float, float], yaw: float,
                      observation: np.ndarray, save_path: str = None,
                      distance_array: np.ndarray = None) -> None:
    """
    可视化无人机状态

    Args:
        position: 位置 (x, y, z)
        yaw: 偏航角（度）
        observation: 观测数据（深度图像或距离数组）
        save_path: 保存路径
        distance_array: 距离数组（如果observation是深度图像，可以提供对应的距离数组）
    """
    # 检查输入类型
    is_depth_image = len(observation.shape) == 2 and observation.shape[0] > 1 and observation.shape[1] > 1

    if is_depth_image:
        # 输入是深度图像
        depth_img = observation
        if distance_array is None:
            # 如果没有提供距离数组，创建一个空的
            distance_array = np.full(72, 100.0)
    else:
        # 输入是距离数组
        depth_img = None
        distance_array = observation

    # 创建图形
    if is_depth_image:
        # 如果有深度图像，创建3个子图
        fig = plt.figure(figsize=(18, 6))
        ax1 = fig.add_subplot(131)  # 1行3列的第1个子图
        ax2 = fig.add_subplot(132, projection='polar')  # 1行3列的第2个子图，极坐标
        ax3 = fig.add_subplot(133)  # 1行3列的第3个子图
    else:
        # 如果只有距离数组，创建2个子图
        fig = plt.figure(figsize=(15, 7))
        ax1 = fig.add_subplot(121)  # 1行2列的第1个子图
        ax2 = fig.add_subplot(122, projection='polar')  # 1行2列的第2个子图，极坐标
        ax3 = None

    # 绘制无人机位置和方向
    x, y, z = position
    ax1.plot(x, y, 'ro', markersize=10)

    # 绘制无人机方向
    yaw_rad = np.radians(yaw)
    direction_length = 2.0
    dx = direction_length * np.cos(yaw_rad)
    dy = direction_length * np.sin(yaw_rad)
    ax1.arrow(x, y, dx, dy, head_width=0.5, head_length=0.7, fc='r', ec='r')

    # 绘制距离数据
    max_range = max(10.0, np.max(distance_array) * 1.1)

    # 计算角度（弧度）- 只显示90度视场角
    num_bins = len(distance_array)
    angle_range = 90.0  # 度
    half_range = angle_range / 2

    # 创建角度数组（弧度）
    angles = np.linspace(-np.radians(half_range), np.radians(half_range), num_bins)

    for i, (angle, distance) in enumerate(zip(angles, distance_array)):
        # 计算点的坐标（考虑无人机的偏航角）
        lx = x + distance * np.cos(angle + yaw_rad)
        ly = y + distance * np.sin(angle + yaw_rad)

        # 绘制线
        ax1.plot([x, lx], [y, ly], 'g-', alpha=0.3)

        # 绘制点
        ax1.plot(lx, ly, 'go', markersize=3)

    # 设置坐标轴范围
    ax1.set_xlim(x - max_range, x + max_range)
    ax1.set_ylim(y - max_range, y + max_range)

    # 添加标题和标签
    ax1.set_title(f"Drone Position (Height: {-z:.2f}m, Yaw: {yaw:.2f}°)")
    ax1.set_xlabel("X (m)")
    ax1.set_ylabel("Y (m)")

    # 添加网格
    ax1.grid(True)

    # 保持纵横比例一致
    ax1.set_aspect('equal')

    # 绘制极坐标图中的距离数据
    # 计算角度（弧度）- 只显示90度视场角
    num_bins = len(distance_array)
    angle_range = 90.0  # 度
    half_range = angle_range / 2

    # 创建角度数组（弧度）
    polar_angles = np.linspace(-np.radians(half_range), np.radians(half_range), num_bins)

    ax2.plot(polar_angles, distance_array, 'o-', linewidth=2)
    ax2.fill(polar_angles, distance_array, alpha=0.25)

    # 设置方向标签
    ax2.set_theta_zero_location('N')  # 0度在上方（北）
    ax2.set_theta_direction(-1)  # 顺时针方向

    # 设置半径范围
    ax2.set_ylim(0, max_range)

    # 设置角度范围
    ax2.set_thetamin(-half_range)
    ax2.set_thetamax(half_range)

    # 添加标题
    ax2.set_title("Distance Data (Polar)")

    # 添加网格
    ax2.grid(True)

    # 如果有深度图像，绘制深度图像
    if is_depth_image and ax3 is not None:
        # 绘制深度图像
        im = ax3.imshow(depth_img, cmap='jet')
        ax3.set_title("Depth Image")

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax3)
        cbar.set_label('Depth (m)')

    # 调整布局
    plt.tight_layout()

    # 如果指定了保存路径，保存图像
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()
