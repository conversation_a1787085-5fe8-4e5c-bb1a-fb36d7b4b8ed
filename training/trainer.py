"""
模型训练模块
"""
import numpy as np
import os
import time
import matplotlib.pyplot as plt
from typing import List, Tuple, Dict, Any

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import EPOCHS, BATCH_SIZE, MODEL_DIR
from models.student_model import StudentModel

class Trainer:
    """
    模型训练器
    """
    def __init__(self, student: StudentModel, model_dir: str = MODEL_DIR):
        """
        初始化训练器

        Args:
            student: 学生模型
            model_dir: 模型存储目录
        """
        self.student = student
        self.model_dir = model_dir

        # 创建模型存储目录
        os.makedirs(model_dir, exist_ok=True)

    def train(self, observations: np.ndarray, actions: np.ndarray, epochs: int = EPOCHS, batch_size: int = BATCH_SIZE) -> List[float]:
        """
        训练模型

        Args:
            observations: 观测数据
            actions: 动作数据
            epochs: 训练轮数
            batch_size: 批次大小

        Returns:
            训练损失历史
        """
        print(f"Starting training: {epochs} epochs, {batch_size} batch size")

        # 训练模型
        loss_history = self.student.train(observations, actions, epochs, batch_size)

        # 保存模型
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        model_path = os.path.join(self.model_dir, f"student_model_{timestamp}.pth")
        self.student.save(model_path)

        # 保存最新模型
        latest_model_path = os.path.join(self.model_dir, "student_model_latest.pth")
        self.student.save(latest_model_path)

        print(f"Training completed: final loss {loss_history[-1]:.6f}")

        return loss_history

    def plot_loss(self, loss_history: List[float], save_path: str = None) -> None:
        """
        绘制损失曲线

        Args:
            loss_history: 损失历史
            save_path: 保存路径
        """
        plt.figure(figsize=(10, 6))
        plt.plot(loss_history)
        plt.title("Training Loss")
        plt.xlabel("Epoch")
        plt.ylabel("Loss")
        plt.grid(True)

        if save_path is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(self.model_dir, f"loss_curve_{timestamp}.png")

        plt.savefig(save_path)
        plt.close()

        print(f"Loss curve saved to {save_path}")

    def evaluate(self, observations: np.ndarray, actions: np.ndarray) -> float:
        """
        评估模型

        Args:
            observations: 观测数据（深度图像）
            actions: 动作数据

        Returns:
            平均误差
        """
        print("Evaluating model...")

        # 预测动作
        predicted_actions = []
        for i, observation in enumerate(observations):
            if i % 100 == 0:
                print(f"Evaluating sample {i}/{len(observations)}")

            action = self.student.predict(observation)
            predicted_actions.append(action)

        # 计算误差
        predicted_actions = np.array(predicted_actions)
        true_actions = actions

        # 确保形状匹配
        if len(predicted_actions.shape) == 1 and len(true_actions.shape) == 2:
            predicted_actions = predicted_actions.reshape(-1, 1)
        elif len(predicted_actions.shape) == 2 and len(true_actions.shape) == 1:
            true_actions = true_actions.reshape(-1, 1)

        mse = np.mean((predicted_actions - true_actions) ** 2)
        mae = np.mean(np.abs(predicted_actions - true_actions))

        print(f"Evaluation results: MSE = {mse:.6f}, MAE = {mae:.6f}")

        # 如果是3维动作，分别计算每个维度的误差
        if len(predicted_actions.shape) == 2 and predicted_actions.shape[1] == 3:
            for i, dim_name in enumerate(['Vx', 'Vy', 'Vz']):
                dim_mse = np.mean((predicted_actions[:, i] - true_actions[:, i]) ** 2)
                dim_mae = np.mean(np.abs(predicted_actions[:, i] - true_actions[:, i]))
                print(f"  {dim_name}: MSE = {dim_mse:.6f}, MAE = {dim_mae:.6f}")

        # 绘制预测值与真实值的对比图
        self._plot_prediction_comparison(predicted_actions, true_actions)

        return mae

    def _plot_prediction_comparison(self, predicted_actions: np.ndarray, true_actions: np.ndarray,
                                   max_samples: int = 1000) -> None:
        """
        绘制预测值与真实值的对比图

        Args:
            predicted_actions: 预测的动作
            true_actions: 真实的动作
            max_samples: 最大样本数
        """
        # 限制样本数量
        if len(predicted_actions) > max_samples:
            indices = np.random.choice(len(predicted_actions), max_samples, replace=False)
            predicted_actions = predicted_actions[indices]
            true_actions = true_actions[indices]

        # 检查是否是3维动作
        is_3d = len(predicted_actions.shape) == 2 and predicted_actions.shape[1] == 3

        if is_3d:
            # 3维动作的可视化
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))

            dim_names = ['Vx', 'Vy', 'Vz']
            for i, dim_name in enumerate(dim_names):
                # 散点图
                axes[0, i].scatter(true_actions[:, i], predicted_actions[:, i], alpha=0.5)
                min_val = min(true_actions[:, i].min(), predicted_actions[:, i].min())
                max_val = max(true_actions[:, i].max(), predicted_actions[:, i].max())
                axes[0, i].plot([min_val, max_val], [min_val, max_val], 'r--')
                axes[0, i].set_xlabel(f"True {dim_name}")
                axes[0, i].set_ylabel(f"Predicted {dim_name}")
                axes[0, i].set_title(f"{dim_name}: Prediction vs Ground Truth")
                axes[0, i].grid(True)

                # 误差直方图
                errors = predicted_actions[:, i] - true_actions[:, i]
                axes[1, i].hist(errors, bins=50)
                axes[1, i].set_xlabel(f"{dim_name} Prediction Error")
                axes[1, i].set_ylabel("Frequency")
                axes[1, i].set_title(f"{dim_name} Error (MAE: {np.mean(np.abs(errors)):.3f})")
                axes[1, i].grid(True)
        else:
            # 1维动作的可视化
            plt.figure(figsize=(12, 6))

            # 绘制散点图
            plt.subplot(1, 2, 1)
            plt.scatter(true_actions, predicted_actions, alpha=0.5)
            min_val = min(true_actions.min(), predicted_actions.min())
            max_val = max(true_actions.max(), predicted_actions.max())
            plt.plot([min_val, max_val], [min_val, max_val], 'r--')  # 对角线
            plt.xlabel("True Actions")
            plt.ylabel("Predicted Actions")
            plt.title("Prediction vs Ground Truth")
            plt.grid(True)

            # 绘制误差直方图
            plt.subplot(1, 2, 2)
            errors = predicted_actions - true_actions
            plt.hist(errors, bins=50)
            plt.xlabel("Prediction Error")
            plt.ylabel("Frequency")
            plt.title(f"Error Distribution (MAE: {np.mean(np.abs(errors)):.2f})")
            plt.grid(True)

        # 保存图形
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(self.model_dir, f"prediction_comparison_{timestamp}.png")
        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()

        print(f"Prediction comparison saved to {save_path}")
