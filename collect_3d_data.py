#!/usr/bin/env python3
"""
收集3D避障训练数据
"""
import os
import time
import argparse
import numpy as np

from environment.drone_env import DroneEnvironment
from models.teacher_model import TeacherModel
from training.data_collector import DataCollector
from config import DATA_COLLECTION_EPISODES, DATA_COLLECTION_STEPS_PER_EPISODE, MIN_OBSTACLE_DISTANCE

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Collect 3D obstacle avoidance training data")
    parser.add_argument("--episodes", type=int, default=DATA_COLLECTION_EPISODES, help="Number of episodes to collect")
    parser.add_argument("--steps", type=int, default=DATA_COLLECTION_STEPS_PER_EPISODE, help="Steps per episode")
    parser.add_argument("--visualize", action="store_true", help="Visualize data collection")
    parser.add_argument("--save_episodes", action="store_true", help="Save data for each episode")
    parser.add_argument("--disable_collision", action="store_true", help="Disable collision detection")
    parser.add_argument("--min_distance", type=float, default=MIN_OBSTACLE_DISTANCE, help="Minimum obstacle distance")
    
    return parser.parse_args()

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    print("开始收集3D避障训练数据...")
    print(f"回合数: {args.episodes}")
    print(f"每回合步数: {args.steps}")
    print(f"可视化: {args.visualize}")
    print(f"碰撞检测: {'禁用' if args.disable_collision else '启用'}")
    print(f"最小障碍物距离: {args.min_distance}米")
    
    try:
        # 创建环境和教师模型
        env = DroneEnvironment(
            enable_collision_detection=not args.disable_collision,
            min_obstacle_distance=args.min_distance
        )
        teacher = TeacherModel(min_distance=args.min_distance)
        
        # 创建数据收集器
        data_collector = DataCollector(
            env=env,
            teacher=teacher,
            save_episodes=args.save_episodes,
            visualize=args.visualize,
            visualize_interval=20  # 每20步可视化一次
        )
        
        # 收集数据
        observations, actions = data_collector.collect_data(
            episodes=args.episodes,
            max_steps=args.steps
        )
        
        # 检查数据
        print(f"\n数据收集完成！")
        print(f"观测数据形状: {observations.shape}")
        print(f"动作数据形状: {actions.shape}")
        
        # 验证数据类型
        if len(actions.shape) == 2 and actions.shape[1] == 3:
            print("✅ 成功收集3维速度向量数据")
            print(f"速度统计:")
            print(f"  Vx: 平均={np.mean(actions[:, 0]):.2f}, 标准差={np.std(actions[:, 0]):.2f}")
            print(f"  Vy: 平均={np.mean(actions[:, 1]):.2f}, 标准差={np.std(actions[:, 1]):.2f}")
            print(f"  Vz: 平均={np.mean(actions[:, 2]):.2f}, 标准差={np.std(actions[:, 2]):.2f}")
        else:
            print("⚠️ 动作数据格式可能不正确")
        
        # 保存最终数据
        data_collector.save_data("final")
        
        print("\n数据收集任务完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断数据收集")
    except Exception as e:
        print(f"\n数据收集过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保环境正确关闭
        try:
            env.close()
        except:
            pass

if __name__ == "__main__":
    main()
