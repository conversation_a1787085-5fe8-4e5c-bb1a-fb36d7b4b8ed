"""
教师模型（3DVFH+算法）
"""
import numpy as np
import math
from typing import List, Tuple, Dict, Any

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    MIN_OBSTACLE_DISTANCE, DRONE_RADIUS, DRONE_HEIGHT,
    CAMERA_FOV_H, CAMERA_FOV_V, DEPTH_SCALE, MAX_DEPTH, SKY_DEPTH,
    MAX_SPEED, MAX_CLIMB_RATE, MAX_DESCENT_RATE
)
from environment.depth_utils import (
    process_depth_image, depth_to_point_cloud
)
from environment.vfh3d_utils import VFH3DPlanner

class TeacherModel:
    """
    基于3DVFH+算法的避障教师模型
    """
    def __init__(self, min_distance: float = MIN_OBSTACLE_DISTANCE):
        """
        初始化教师模型

        Args:
            min_distance: 最小安全距离
        """
        self.min_distance = min_distance

        # 初始化3DVFH+规划器
        self.vfh_planner = VFH3DPlanner()

        # 缓存最近的数据，用于可视化和调试
        self.last_depth_image = None
        self.last_point_cloud = None
        self.last_velocity = np.array([MAX_SPEED, 0.0, 0.0])  # 当前速度向量

    def process_depth_image(self, depth_img: np.ndarray) -> Dict[str, Any]:
        """
        处理深度图像，提取点云

        Args:
            depth_img: 深度图像

        Returns:
            处理结果字典
        """
        # 缓存深度图像
        self.last_depth_image = depth_img

        # 处理深度图像（处理无效值和天空）
        processed_depth = process_depth_image(depth_img, MAX_DEPTH, SKY_DEPTH)

        # 将深度图像转换为点云
        points = depth_to_point_cloud(processed_depth, CAMERA_FOV_H, DEPTH_SCALE)

        # 缓存点云
        self.last_point_cloud = points

        # 计算最小距离
        if len(points) > 0:
            distances = np.linalg.norm(points, axis=1)
            min_distance = np.min(distances)
        else:
            min_distance = MAX_DEPTH

        return {
            "points": points,
            "min_distance": min_distance
        }

    def get_safe_velocity(self, observation: np.ndarray) -> np.ndarray:
        """
        获取安全的3维速度向量

        Args:
            observation: 深度图像

        Returns:
            安全的3维速度向量 (Vx, Vy, Vz)
        """
        # 检查输入类型
        if len(observation.shape) == 2:
            # 输入是深度图像
            result = self.process_depth_image(observation)
            points = result["points"]
            min_distance = result["min_distance"]

            # 打印调试信息
            print(f"[3DVFH+调试] 最小障碍物距离: {min_distance:.2f}m")
            print(f"[3DVFH+调试] 点云数量: {len(points)}")

            # 使用3DVFH+算法规划速度
            velocity = self.vfh_planner.plan_velocity(points, self.last_velocity)

            # 更新当前速度
            self.last_velocity = velocity

            return velocity
        else:
            # 如果输入不是深度图像，返回默认前进速度
            print(f"[3DVFH+调试] 输入不是深度图像，使用默认前进速度")
            return np.array([MAX_SPEED, 0.0, 0.0])

    def get_safe_direction(self, observation: np.ndarray) -> float:
        """
        为了兼容性保留的方法，将3D速度转换为2D转向角

        Args:
            observation: 深度图像

        Returns:
            转向角（度）
        """
        velocity = self.get_safe_velocity(observation)

        # 将速度向量转换为转向角
        if abs(velocity[0]) > 1e-6:  # 避免除零
            turn_angle = math.degrees(math.atan2(velocity[1], velocity[0]))
        else:
            turn_angle = 0.0

        print(f"[兼容性] 3D速度 ({velocity[0]:.2f}, {velocity[1]:.2f}, {velocity[2]:.2f}) -> 转向角 {turn_angle:.2f}度")
        return turn_angle
