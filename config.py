"""
配置文件，包含项目的所有配置参数
"""

# AirSim连接配置
AIRSIM_IP = "127.0.0.1"
AIRSIM_PORT = 41451

# 无人机配置
DRONE_NAME = "Drone1"  # 根据settings_rgb_deep.json中的配置
TAKEOFF_HEIGHT = 5.0  # 起飞高度（米）
MAX_SPEED = 3.0  # 最大飞行速度（米/秒）
DRONE_RADIUS = 0.3  # 无人机半径（米）
DRONE_HEIGHT = 0.2  # 无人机高度（米）

# 相机配置
CAMERA_NAME = "front_center"  # 相机名称
CAMERA_FOV_H = 90.0  # 水平视场角（度）
CAMERA_FOV_V = 60.0  # 垂直视场角（度）
DEPTH_WIDTH = 128  # 深度图像宽度
DEPTH_HEIGHT = 72  # 深度图像高度
DEPTH_SCALE = 1.0  # 深度缩放因子（经测试，深度值不需要缩放）
MAX_DEPTH = 100.0  # 最大深度值（米），超过此值的被视为天空
SKY_DEPTH = 100.0  # 天空的深度值（米）

# 3DVFH+算法配置
VFH_ALPHA_RESOLUTION = 5  # 水平角度分辨率（度）
VFH_BETA_RESOLUTION = 5   # 垂直角度分辨率（度）
VFH_THRESHOLD = 0.5       # VFH阈值
VFH_SAFETY_RADIUS = 1.0   # 安全半径（米）
VFH_WINDOW_SIZE = 5       # 平滑窗口大小

# 3D避障配置
MIN_OBSTACLE_DISTANCE = 3.0  # 最小障碍物距离（米）
SAFE_DISTANCE = 5.0          # 安全距离（米）
VERTICAL_CLEARANCE = 2.0     # 垂直间隙（米）
MAX_CLIMB_RATE = 1.0         # 最大爬升率（米/秒）
MAX_DESCENT_RATE = 1.0       # 最大下降率（米/秒）

# 训练配置
DATA_COLLECTION_EPISODES = 100  # 数据收集的回合数
DATA_COLLECTION_STEPS_PER_EPISODE = 1000  # 每回合的步数
DATA_DIR = "data"  # 数据存储目录
MODEL_DIR = "saved_models"  # 模型存储目录

# 学生模型配置 - CNN+FC架构
# 输入为深度图像，输出为3维速度向量
INPUT_CHANNELS = 1  # 输入通道数（深度图像为单通道）
INPUT_WIDTH = DEPTH_WIDTH  # 输入宽度
INPUT_HEIGHT = DEPTH_HEIGHT  # 输入高度
CONV_FILTERS = [16, 32, 64]  # 卷积层滤波器数量
CONV_KERNEL_SIZES = [5, 3, 3]  # 卷积核大小
CONV_STRIDES = [2, 2, 2]  # 卷积步长
FC_SIZES = [256, 128, 64]  # 全连接层大小
OUTPUT_SIZE = 3  # 输出大小（3维速度向量：Vx, Vy, Vz）
LEARNING_RATE = 0.001  # 学习率
BATCH_SIZE = 32  # 批次大小
EPOCHS = 100  # 训练轮数
