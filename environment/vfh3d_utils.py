"""
3DVFH+算法实现
基于Vector Field Histogram的3维避障算法
"""
import numpy as np
import math
from typing import List, Tu<PERSON>, Dict, Any

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    VFH_ALPHA_RESOLUTION, VFH_BETA_RESOLUTION, VFH_THRESHOLD,
    VFH_SAFETY_RADIUS, VFH_WINDOW_SIZE, DRONE_RADIUS, DRONE_HEIGHT,
    MIN_OBSTACLE_DISTANCE, SAFE_DISTANCE, VERTICAL_CLEARANCE,
    MAX_SPEED, MAX_CLIMB_RATE, MAX_DESCENT_RATE
)

class VFH3DPlanner:
    """
    3DVFH+路径规划器
    """
    def __init__(self):
        """
        初始化3DVFH+规划器
        """
        self.alpha_resolution = VFH_ALPHA_RESOLUTION  # 水平角度分辨率
        self.beta_resolution = VFH_BETA_RESOLUTION    # 垂直角度分辨率
        self.threshold = VFH_THRESHOLD                # VFH阈值
        self.safety_radius = VFH_SAFETY_RADIUS        # 安全半径
        self.window_size = VFH_WINDOW_SIZE            # 平滑窗口大小

        # 计算角度网格
        self.alpha_bins = int(360 / self.alpha_resolution)  # 水平角度分区数
        self.beta_bins = int(180 / self.beta_resolution)    # 垂直角度分区数

        # 创建角度数组
        self.alpha_angles = np.linspace(0, 360, self.alpha_bins, endpoint=False)
        self.beta_angles = np.linspace(-90, 90, self.beta_bins)

        # 历史直方图用于平滑
        self.hist_buffer = []

    def points_to_histogram(self, points: np.ndarray, drone_pos: np.ndarray = None) -> np.ndarray:
        """
        将点云转换为3D直方图

        Args:
            points: 点云数组 (N, 3)
            drone_pos: 无人机位置 (3,)，默认为原点

        Returns:
            3D直方图 (alpha_bins, beta_bins)
        """
        if drone_pos is None:
            drone_pos = np.array([0.0, 0.0, 0.0])

        # 初始化直方图
        histogram = np.zeros((self.alpha_bins, self.beta_bins))

        if len(points) == 0:
            return histogram

        # 计算相对位置
        relative_points = points - drone_pos

        # 计算距离
        distances = np.linalg.norm(relative_points, axis=1)

        # 过滤太远的点
        valid_mask = distances < SAFE_DISTANCE * 2
        if not np.any(valid_mask):
            return histogram

        valid_points = relative_points[valid_mask]
        valid_distances = distances[valid_mask]

        # 计算球坐标
        x, y, z = valid_points[:, 0], valid_points[:, 1], valid_points[:, 2]

        # 水平角度 (azimuth)
        alpha = np.arctan2(y, x) * 180 / np.pi
        alpha = (alpha + 360) % 360  # 转换到[0, 360)

        # 垂直角度 (elevation)
        r_xy = np.sqrt(x**2 + y**2)
        beta = np.arctan2(z, r_xy) * 180 / np.pi
        beta = np.clip(beta, -90, 90)  # 限制在[-90, 90]

        # 计算障碍物密度
        for i in range(len(valid_points)):
            # 计算角度索引
            alpha_idx = int(alpha[i] / self.alpha_resolution) % self.alpha_bins
            beta_idx = int((beta[i] + 90) / self.beta_resolution)
            beta_idx = np.clip(beta_idx, 0, self.beta_bins - 1)

            # 计算障碍物强度（距离越近强度越大）
            distance = valid_distances[i]
            if distance < MIN_OBSTACLE_DISTANCE:
                intensity = 1.0
            else:
                intensity = max(0.0, 1.0 - (distance - MIN_OBSTACLE_DISTANCE) / SAFE_DISTANCE)

            # 考虑无人机尺寸的影响
            size_factor = (DRONE_RADIUS + self.safety_radius) / max(distance, 0.1)
            intensity = min(1.0, intensity + size_factor)

            histogram[alpha_idx, beta_idx] = max(histogram[alpha_idx, beta_idx], intensity)

        return histogram

    def smooth_histogram(self, histogram: np.ndarray) -> np.ndarray:
        """
        平滑直方图

        Args:
            histogram: 输入直方图

        Returns:
            平滑后的直方图
        """
        # 添加到历史缓冲区
        self.hist_buffer.append(histogram.copy())
        if len(self.hist_buffer) > self.window_size:
            self.hist_buffer.pop(0)

        # 时间平滑
        smoothed = np.mean(self.hist_buffer, axis=0)

        # 空间平滑（简单的邻域平均）
        try:
            from scipy import ndimage
            smoothed = ndimage.gaussian_filter(smoothed, sigma=1.0, mode='wrap')
        except ImportError:
            # 如果没有scipy，使用简单的邻域平均
            kernel_size = 3
            padded = np.pad(smoothed, ((kernel_size//2, kernel_size//2), (kernel_size//2, kernel_size//2)), mode='wrap')
            for i in range(smoothed.shape[0]):
                for j in range(smoothed.shape[1]):
                    smoothed[i, j] = np.mean(padded[i:i+kernel_size, j:j+kernel_size])

        return smoothed

    def find_safe_directions(self, histogram: np.ndarray, current_velocity: np.ndarray = None) -> List[Tuple[float, float, float]]:
        """
        寻找安全飞行方向

        Args:
            histogram: 障碍物直方图
            current_velocity: 当前速度向量 (3,)

        Returns:
            安全方向列表 [(alpha, beta, score), ...]
        """
        if current_velocity is None:
            current_velocity = np.array([MAX_SPEED, 0.0, 0.0])  # 默认向前飞行

        safe_directions = []

        # 遍历所有可能的方向
        for alpha_idx in range(self.alpha_bins):
            for beta_idx in range(self.beta_bins):
                alpha = self.alpha_angles[alpha_idx]
                beta = self.beta_angles[beta_idx]

                # 检查该方向是否安全
                if histogram[alpha_idx, beta_idx] < self.threshold:
                    # 计算方向得分
                    score = self._calculate_direction_score(alpha, beta, current_velocity, histogram)
                    safe_directions.append((alpha, beta, score))

        # 按得分排序
        safe_directions.sort(key=lambda x: x[2], reverse=True)

        return safe_directions

    def _calculate_direction_score(self, alpha: float, beta: float, current_velocity: np.ndarray, histogram: np.ndarray) -> float:
        """
        计算方向得分

        Args:
            alpha: 水平角度
            beta: 垂直角度
            current_velocity: 当前速度向量
            histogram: 障碍物直方图

        Returns:
            方向得分
        """
        # 转换为单位向量
        alpha_rad = math.radians(alpha)
        beta_rad = math.radians(beta)

        direction = np.array([
            math.cos(beta_rad) * math.cos(alpha_rad),
            math.cos(beta_rad) * math.sin(alpha_rad),
            math.sin(beta_rad)
        ])

        # 当前速度方向
        current_dir = current_velocity / (np.linalg.norm(current_velocity) + 1e-6)

        # 方向一致性得分
        consistency_score = np.dot(direction, current_dir)

        # 安全性得分（周围区域的障碍物密度）
        alpha_idx = int(alpha / self.alpha_resolution) % self.alpha_bins
        beta_idx = int((beta + 90) / self.beta_resolution)
        beta_idx = np.clip(beta_idx, 0, self.beta_bins - 1)

        safety_score = 1.0 - histogram[alpha_idx, beta_idx]

        # 周围区域的安全性
        neighborhood_safety = 0.0
        count = 0
        for da in [-1, 0, 1]:
            for db in [-1, 0, 1]:
                na_idx = (alpha_idx + da) % self.alpha_bins
                nb_idx = np.clip(beta_idx + db, 0, self.beta_bins - 1)
                neighborhood_safety += 1.0 - histogram[na_idx, nb_idx]
                count += 1
        neighborhood_safety /= count

        # 高度偏好（稍微偏向水平飞行）
        height_preference = math.cos(beta_rad)

        # 综合得分
        total_score = (
            0.4 * consistency_score +
            0.3 * safety_score +
            0.2 * neighborhood_safety +
            0.1 * height_preference
        )

        return total_score

    def plan_velocity(self, points: np.ndarray, current_velocity: np.ndarray = None, target_direction: np.ndarray = None) -> np.ndarray:
        """
        规划3维速度向量

        Args:
            points: 点云数组 (N, 3)
            current_velocity: 当前速度向量 (3,)
            target_direction: 目标方向 (3,)，可选

        Returns:
            规划的速度向量 (3,)
        """
        if current_velocity is None:
            current_velocity = np.array([MAX_SPEED, 0.0, 0.0])

        # 生成障碍物直方图
        histogram = self.points_to_histogram(points)

        # 平滑直方图
        smoothed_histogram = self.smooth_histogram(histogram)

        # 寻找安全方向
        safe_directions = self.find_safe_directions(smoothed_histogram, current_velocity)

        if not safe_directions:
            # 没有安全方向，紧急制动
            print("警告：没有找到安全方向，执行紧急制动")
            return np.array([0.0, 0.0, 0.0])

        # 选择最佳方向
        best_alpha, best_beta, best_score = safe_directions[0]

        # 转换为速度向量
        alpha_rad = math.radians(best_alpha)
        beta_rad = math.radians(best_beta)

        # 计算速度分量
        speed = MAX_SPEED
        vx = speed * math.cos(beta_rad) * math.cos(alpha_rad)
        vy = speed * math.cos(beta_rad) * math.sin(alpha_rad)
        vz = speed * math.sin(beta_rad)

        # 限制垂直速度
        vz = np.clip(vz, -MAX_DESCENT_RATE, MAX_CLIMB_RATE)

        velocity = np.array([vx, vy, vz])

        print(f"3DVFH+规划结果: 方向=({best_alpha:.1f}°, {best_beta:.1f}°), 得分={best_score:.3f}, 速度=({vx:.2f}, {vy:.2f}, {vz:.2f})")

        return velocity
