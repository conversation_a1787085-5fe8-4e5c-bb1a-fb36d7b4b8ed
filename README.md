# 3D无人机避障项目

本项目实现了基于深度相机的3维无人机避障系统，使用3DVFH+算法作为教师模型，通过模仿学习训练神经网络学生模型。

## 项目概述

- 无人机起飞到5m高度，使用3维速度控制 (Vx, Vy, Vz)
- 使用深度相机获取前方环境的深度图像
- 将深度图像转换为3D点云，使用3DVFH+算法进行3维路径规划
- 教师模型输出3维速度向量，学生模型学习模仿这种行为
- 采用模仿学习方法，先使用3DVFH+算法作为教师模型，然后训练CNN学生模型

## 项目结构

```
deep_il/
├── environment/           # 无人机环境封装
│   ├── __init__.py
│   ├── drone_env.py       # 无人机环境封装
│   ├── depth_utils.py     # 深度图像处理工具
│   └── vfh3d_utils.py     # 3DVFH+算法实现
├── models/                # 模型定义
│   ├── __init__.py
│   ├── teacher_model.py   # 3DVFH+算法作为教师模型
│   └── student_model.py   # 学生模型（CNN+FC神经网络，输出3维速度）
├── training/              # 训练相关
│   ├── __init__.py
│   ├── data_collector.py  # 数据收集
│   └── trainer.py         # 模型训练
├── utils/                 # 工具函数
│   ├── __init__.py
│   └── visualization.py   # 可视化工具
├── config.py              # 配置文件
├── run_teacher.py         # 运行教师模型
├── collect_data.py        # 收集训练数据
├── train_model.py         # 训练学生模型
├── run_student.py         # 运行学生模型
├── test_depth_image.py    # 深度图像测试脚本
└── settings_rgb_deep.json # AirSim配置文件
```

## 安装依赖

```bash
pip install airsim numpy torch matplotlib
```

## 使用方法

### 1. 运行教师模型

```bash
python run_teacher.py --max_steps 50 --visualize
```

### 2. 收集训练数据

```bash
python collect_data.py --episodes 100 --steps 50
```

### 3. 训练学生模型

```bash
python train_model.py --data_path data/depth_data_YYYYMMDD_HHMMSS_final.npz --epochs 100
```

### 4. 运行学生模型

```bash
python run_student.py --model_path saved_models/student_model_latest.pth --max_steps 50 --visualize
```

## 配置参数

可以在`config.py`文件中修改以下参数：

- 无人机配置：起飞高度、前进速度、无人机半径
- 相机配置：相机名称、视场角、深度图像分辨率、深度缩放因子
- 点云配置：角度分区数量、角度范围、碰撞检测方向数量
- 避障配置：最小障碍物距离、最大转向角
- 学生模型配置：CNN+FC架构参数、学习率、批次大小等
- 训练配置：数据收集回合数、训练轮数等

## 实现细节

### 深度图像处理

1. 获取深度图像：使用AirSim的API获取深度图像
2. 处理深度图像：将超过100米的深度值（天空）设为100米
3. 转换为点云：将深度图像转换为3D点云
4. 3D避障：使用3DVFH+算法进行3维路径规划

### 教师模型（3DVFH+算法）

教师模型基于3DVFH+算法，根据点云数据计算安全的3维速度向量：
1. 将点云转换为3D直方图，表示不同方向的障碍物密度
2. 对直方图进行时间和空间平滑
3. 寻找安全的飞行方向（水平角度α和垂直角度β）
4. 根据安全性、方向一致性等因素对候选方向评分
5. 选择最佳方向并转换为3维速度向量 (Vx, Vy, Vz)

### 学生模型

学生模型使用CNN+FC神经网络架构：
1. 输入为深度图像（单通道）
2. 使用卷积层提取特征
3. 使用全连接层输出3维速度向量 (Vx, Vy, Vz)

### 数据收集

使用教师模型控制无人机飞行，收集深度图像和对应的3维速度向量作为训练数据。

### 模型训练

使用收集的数据训练学生模型，使其模仿教师模型的3维避障行为。

## 其他说明

- `test_depth_image.py`：测试深度图像的获取和处理，验证深度图像的缩放因子和有效范围
- `simple_check.py`：检查data下收集的数据最后收集成功的文件内容是不是前面收集的所有的总和
- `test_drone_600m.py`：起飞600米高度来俯瞰下方环境布局（需配合settings_downrgb.json的下视相机使用）

## 3D避障说明

- 深度图像中深度值超过100米的被视作天空，将被强制设为100米的深度值
- 经测试，深度图像不需要缩放，测量出来的深度值即为实际值
- 无人机使用3维速度控制 (Vx, Vy, Vz)，可以在3维空间中自由移动
- 3DVFH+算法考虑水平和垂直两个维度的避障，提供更灵活的路径规划
- 学生模型直接输出3维速度向量，无需转换为角度控制


## 3DVFH+算法设计思路总结：

### 原2D方法回顾：
原项目使用2D避障方法：将深度图像转为3D点云，然后在水平面上划分为72个方向区间（每个区间2.5度），每个区间取最小水平距离作为该方向的障碍物距离，最终输出转向角。

### 新3DVFH+方法：
1. **3D直方图构建**：将点云映射到3D球坐标系统，使用水平角度α（0-360°）和垂直角度β（-90°到90°）构建3D直方图
2. **障碍物密度计算**：考虑点云距离、无人机尺寸等因素，计算每个方向的障碍物密度
3. **时间和空间平滑**：对直方图进行时间平滑（多帧平均）和空间平滑（邻域平均）
4. **安全方向搜索**：在3D空间中搜索安全的飞行方向，考虑方向一致性、安全性、高度偏好等因素
5. **速度向量输出**：直接输出3维速度向量(Vx, Vy, Vz)，支持真正的3维避障

### 主要改进：
- 从2D转向角控制升级为3D速度向量控制
- 支持垂直方向的避障（爬升/下降）
- 更精确的3D空间障碍物表示
- 更灵活的路径规划能力