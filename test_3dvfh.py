#!/usr/bin/env python3
"""
测试3DVFH+算法
"""
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from environment.vfh3d_utils import VFH3DPlanner
from models.teacher_model import TeacherModel
from utils.visualization import plot_velocity_vector

def create_test_point_cloud():
    """
    创建测试点云数据
    """
    # 创建一些障碍物点
    points = []
    
    # 前方障碍物墙
    for x in np.linspace(8, 12, 20):
        for z in np.linspace(-2, 2, 10):
            points.append([x, 0, z])
    
    # 右侧障碍物
    for y in np.linspace(3, 6, 15):
        for z in np.linspace(-1, 3, 8):
            points.append([5, y, z])
    
    # 上方障碍物
    for x in np.linspace(6, 8, 10):
        for y in np.linspace(-2, 2, 8):
            points.append([x, y, 4])
    
    return np.array(points)

def test_vfh3d_planner():
    """
    测试3DVFH+规划器
    """
    print("测试3DVFH+规划器...")
    
    # 创建规划器
    planner = VFH3DPlanner()
    
    # 创建测试点云
    points = create_test_point_cloud()
    print(f"创建了 {len(points)} 个障碍物点")
    
    # 当前速度（向前飞行）
    current_velocity = np.array([3.0, 0.0, 0.0])
    
    # 规划速度
    planned_velocity = planner.plan_velocity(points, current_velocity)
    
    print(f"当前速度: {current_velocity}")
    print(f"规划速度: {planned_velocity}")
    
    # 可视化结果
    fig = plt.figure(figsize=(15, 5))
    
    # 子图1：点云
    ax1 = fig.add_subplot(131, projection='3d')
    ax1.scatter(points[:, 0], points[:, 1], points[:, 2], c='red', s=20, alpha=0.6)
    ax1.set_xlabel('X (forward)')
    ax1.set_ylabel('Y (right)')
    ax1.set_zlabel('Z (up)')
    ax1.set_title('Obstacle Point Cloud')
    
    # 子图2：当前速度
    ax2 = fig.add_subplot(132, projection='3d')
    ax2.quiver(0, 0, 0, current_velocity[0], current_velocity[1], current_velocity[2], 
               color='blue', arrow_length_ratio=0.1, linewidth=3, label='Current')
    ax2.set_xlabel('Vx')
    ax2.set_ylabel('Vy')
    ax2.set_zlabel('Vz')
    ax2.set_title('Current Velocity')
    max_v = max(abs(current_velocity).max(), 1.0)
    ax2.set_xlim([-max_v, max_v])
    ax2.set_ylim([-max_v, max_v])
    ax2.set_zlim([-max_v, max_v])
    
    # 子图3：规划速度
    ax3 = fig.add_subplot(133, projection='3d')
    ax3.quiver(0, 0, 0, planned_velocity[0], planned_velocity[1], planned_velocity[2], 
               color='green', arrow_length_ratio=0.1, linewidth=3, label='Planned')
    ax3.set_xlabel('Vx')
    ax3.set_ylabel('Vy')
    ax3.set_zlabel('Vz')
    ax3.set_title('Planned Velocity')
    max_v = max(abs(planned_velocity).max(), 1.0)
    ax3.set_xlim([-max_v, max_v])
    ax3.set_ylim([-max_v, max_v])
    ax3.set_zlim([-max_v, max_v])
    
    plt.tight_layout()
    plt.savefig('test_3dvfh_result.png')
    plt.show()
    
    return planned_velocity

def test_teacher_model():
    """
    测试教师模型
    """
    print("\n测试教师模型...")
    
    # 创建教师模型
    teacher = TeacherModel()
    
    # 创建模拟深度图像
    depth_img = np.full((72, 128), 50.0)  # 默认50米距离
    
    # 添加一些障碍物
    depth_img[30:42, 50:70] = 8.0  # 前方障碍物
    depth_img[20:30, 80:100] = 12.0  # 右前方障碍物
    depth_img[45:55, 30:50] = 15.0  # 左前方障碍物
    
    # 获取安全速度
    safe_velocity = teacher.get_safe_velocity(depth_img)
    
    print(f"安全速度向量: {safe_velocity}")
    
    # 可视化深度图像
    plt.figure(figsize=(12, 4))
    
    plt.subplot(121)
    plt.imshow(depth_img, cmap='jet')
    plt.colorbar(label='Depth (m)')
    plt.title('Simulated Depth Image')
    
    plt.subplot(122)
    ax = plt.gca(projection='3d')
    ax.quiver(0, 0, 0, safe_velocity[0], safe_velocity[1], safe_velocity[2], 
              color='red', arrow_length_ratio=0.1, linewidth=3)
    ax.set_xlabel('Vx')
    ax.set_ylabel('Vy')
    ax.set_zlabel('Vz')
    ax.set_title('Safe Velocity Vector')
    max_v = max(abs(safe_velocity).max(), 1.0)
    ax.set_xlim([-max_v, max_v])
    ax.set_ylim([-max_v, max_v])
    ax.set_zlim([-max_v, max_v])
    
    plt.tight_layout()
    plt.savefig('test_teacher_model_result.png')
    plt.show()
    
    return safe_velocity

def main():
    """
    主测试函数
    """
    print("开始测试3DVFH+算法和教师模型...")
    
    try:
        # 测试VFH3D规划器
        planned_velocity = test_vfh3d_planner()
        
        # 测试教师模型
        safe_velocity = test_teacher_model()
        
        print("\n测试完成！")
        print(f"VFH3D规划器输出: {planned_velocity}")
        print(f"教师模型输出: {safe_velocity}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
