#!/usr/bin/env python3
"""
测试训练好的3D避障模型
"""
import os
import argparse
import numpy as np
import matplotlib.pyplot as plt

from environment.drone_env import DroneEnvironment
from models.student_model import StudentModel
from models.teacher_model import TeacherModel
from utils.visualization import plot_velocity_vector
from config import MODEL_DIR, MIN_OBSTACLE_DISTANCE

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Test trained 3D obstacle avoidance model")
    parser.add_argument("--model_path", type=str, required=True, help="Path to trained model")
    parser.add_argument("--episodes", type=int, default=5, help="Number of test episodes")
    parser.add_argument("--steps", type=int, default=100, help="Steps per episode")
    parser.add_argument("--compare_teacher", action="store_true", help="Compare with teacher model")
    parser.add_argument("--visualize", action="store_true", help="Visualize predictions")
    parser.add_argument("--disable_collision", action="store_true", help="Disable collision detection")
    
    return parser.parse_args()

def test_model_predictions(student, teacher=None, num_samples=50):
    """
    测试模型预测能力
    """
    print("测试模型预测能力...")
    
    # 创建测试深度图像
    test_images = []
    
    # 场景1：前方有障碍物
    depth1 = np.full((72, 128), 50.0)
    depth1[30:42, 50:70] = 8.0
    test_images.append(("前方障碍物", depth1))
    
    # 场景2：右前方有障碍物
    depth2 = np.full((72, 128), 50.0)
    depth2[20:30, 80:100] = 12.0
    test_images.append(("右前方障碍物", depth2))
    
    # 场景3：左前方有障碍物
    depth3 = np.full((72, 128), 50.0)
    depth3[45:55, 30:50] = 15.0
    test_images.append(("左前方障碍物", depth3))
    
    # 场景4：上方有障碍物
    depth4 = np.full((72, 128), 50.0)
    depth4[10:25, 50:70] = 10.0
    test_images.append(("上方障碍物", depth4))
    
    # 场景5：下方有障碍物
    depth5 = np.full((72, 128), 50.0)
    depth5[50:65, 50:70] = 10.0
    test_images.append(("下方障碍物", depth5))
    
    results = []
    
    for scene_name, depth_img in test_images:
        print(f"\n测试场景: {scene_name}")
        
        # 学生模型预测
        student_velocity = student.predict(depth_img)
        print(f"学生模型预测: Vx={student_velocity[0]:.2f}, Vy={student_velocity[1]:.2f}, Vz={student_velocity[2]:.2f}")
        
        # 教师模型预测（如果提供）
        teacher_velocity = None
        if teacher is not None:
            teacher_velocity = teacher.get_safe_velocity(depth_img)
            print(f"教师模型预测: Vx={teacher_velocity[0]:.2f}, Vy={teacher_velocity[1]:.2f}, Vz={teacher_velocity[2]:.2f}")
            
            # 计算差异
            diff = np.abs(student_velocity - teacher_velocity)
            print(f"预测差异: Vx={diff[0]:.2f}, Vy={diff[1]:.2f}, Vz={diff[2]:.2f}")
        
        results.append({
            'scene': scene_name,
            'depth_img': depth_img,
            'student_velocity': student_velocity,
            'teacher_velocity': teacher_velocity
        })
    
    return results

def visualize_predictions(results):
    """
    可视化预测结果
    """
    print("可视化预测结果...")
    
    num_scenes = len(results)
    fig, axes = plt.subplots(2, num_scenes, figsize=(4*num_scenes, 8))
    
    if num_scenes == 1:
        axes = axes.reshape(2, 1)
    
    for i, result in enumerate(results):
        scene_name = result['scene']
        depth_img = result['depth_img']
        student_velocity = result['student_velocity']
        teacher_velocity = result['teacher_velocity']
        
        # 显示深度图像
        axes[0, i].imshow(depth_img, cmap='jet')
        axes[0, i].set_title(f"{scene_name}\nDepth Image")
        axes[0, i].axis('off')
        
        # 显示速度向量
        ax = axes[1, i]
        ax.remove()
        ax = fig.add_subplot(2, num_scenes, num_scenes + i + 1, projection='3d')
        
        # 学生模型速度向量
        ax.quiver(0, 0, 0, student_velocity[0], student_velocity[1], student_velocity[2], 
                  color='blue', arrow_length_ratio=0.1, linewidth=3, label='Student')
        
        # 教师模型速度向量（如果有）
        if teacher_velocity is not None:
            ax.quiver(0, 0, 0, teacher_velocity[0], teacher_velocity[1], teacher_velocity[2], 
                      color='red', arrow_length_ratio=0.1, linewidth=3, label='Teacher')
        
        # 设置坐标轴
        max_v = max(abs(student_velocity).max(), 
                   abs(teacher_velocity).max() if teacher_velocity is not None else 0, 
                   1.0)
        ax.set_xlim([-max_v, max_v])
        ax.set_ylim([-max_v, max_v])
        ax.set_zlim([-max_v, max_v])
        ax.set_xlabel('Vx')
        ax.set_ylabel('Vy')
        ax.set_zlabel('Vz')
        ax.set_title(f"{scene_name}\nVelocity Vectors")
        ax.legend()
    
    plt.tight_layout()
    plt.savefig('test_predictions.png', dpi=150, bbox_inches='tight')
    plt.show()

def test_in_environment(student, env, episodes=5, steps=100):
    """
    在环境中测试模型
    """
    print(f"在环境中测试模型 ({episodes} 回合, 每回合 {steps} 步)...")
    
    episode_rewards = []
    episode_lengths = []
    collision_count = 0
    
    for episode in range(episodes):
        print(f"\n回合 {episode + 1}/{episodes}")
        
        # 重置环境
        observation = env.reset()
        total_reward = 0
        step_count = 0
        
        for step in range(steps):
            # 使用学生模型预测动作
            action = student.predict(observation)
            
            # 执行动作
            observation, reward, done, info = env.step(action)
            total_reward += reward
            step_count += 1
            
            # 打印信息
            if step % 20 == 0:
                print(f"  步骤 {step}: 动作=[{action[0]:.2f}, {action[1]:.2f}, {action[2]:.2f}], "
                      f"奖励={reward:.2f}, 最小距离={info.get('min_distance', 0):.2f}m")
            
            # 检查是否结束
            if done:
                if info.get('collision', False):
                    collision_count += 1
                    print(f"  回合结束: 碰撞!")
                else:
                    print(f"  回合结束: 正常完成")
                break
        
        episode_rewards.append(total_reward)
        episode_lengths.append(step_count)
        
        print(f"  回合总奖励: {total_reward:.2f}, 步数: {step_count}")
    
    # 统计结果
    avg_reward = np.mean(episode_rewards)
    avg_length = np.mean(episode_lengths)
    collision_rate = collision_count / episodes
    
    print(f"\n测试结果统计:")
    print(f"平均奖励: {avg_reward:.2f}")
    print(f"平均步数: {avg_length:.1f}")
    print(f"碰撞率: {collision_rate:.2%}")
    
    return {
        'avg_reward': avg_reward,
        'avg_length': avg_length,
        'collision_rate': collision_rate,
        'episode_rewards': episode_rewards,
        'episode_lengths': episode_lengths
    }

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    try:
        # 加载学生模型
        print(f"加载学生模型: {args.model_path}")
        student = StudentModel()
        student.load_model(args.model_path)
        print("✅ 学生模型加载成功")
        
        # 创建教师模型（用于对比）
        teacher = None
        if args.compare_teacher:
            print("创建教师模型用于对比...")
            teacher = TeacherModel()
        
        # 测试模型预测能力
        results = test_model_predictions(student, teacher)
        
        # 可视化预测结果
        if args.visualize:
            visualize_predictions(results)
        
        # 在环境中测试（如果不禁用碰撞检测）
        if not args.disable_collision:
            print("\n创建测试环境...")
            env = DroneEnvironment(
                enable_collision_detection=True,
                min_obstacle_distance=MIN_OBSTACLE_DISTANCE
            )
            
            test_results = test_in_environment(student, env, args.episodes, args.steps)
            
            env.close()
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
